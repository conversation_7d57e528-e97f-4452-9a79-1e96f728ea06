#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件告警推送器 - <PERSON><PERSON>式简洁设计
核心原则：基于UID的简单可靠逻辑，消除所有特殊情况
"""

import time
import json
import os
from datetime import datetime, timezone, timedelta
from typing import Set, List, Dict, Any, Optional
import concurrent.futures
import threading
from src.utils.logger import get_logger
from src.utils.performance_monitor import performance_monitor, PerformanceTimer, monitor_performance
from src.email_handler.attachment_handler import attachment_handler
from src.config.config_manager import config_manager

logger = get_logger(__name__)


class EmailAlertPusher:
    """邮件告警推送器 - Linus式简洁设计

    核心原则：
    1. 单一状态：只有seen_uids集合
    2. 简单逻辑：if uid not in seen_uids -> 新邮件
    3. 无特殊情况：没有复杂的时间判断和状态管理
    """

    def __init__(self, email_clients: List, pusher, keywords: List[str]):
        # 核心状态：唯一的真相来源
        self.seen_uids: Set[str] = set()

        # 内存管理配置 - Linus式严格控制
        self.max_seen_uids = 10000  # 最大UID数量，防止内存无限增长
        self.cleanup_threshold = 8000  # 达到此数量时触发清理
        self.cleanup_batch_size = 2000  # 每次清理的数量

        # 性能配置 - 从配置文件读取
        perf_config = config_manager.get_performance_config()
        self.max_emails_per_fetch = perf_config.get("max_emails_per_fetch", 200)
        self.concurrent_timeout = perf_config.get("concurrent_timeout", 30)

        # 状态持久化配置
        self.state_file = os.path.join("config", "seen_uids.json")
        self.max_uid_age_days = 30  # UID最大保存天数
        self.cleanup_counter = 0  # 清理计数器
        self.cleanup_interval = 5  # 每5次检查执行一次清理（更频繁）

        # 依赖注入：简单明确
        self.email_clients = email_clients
        self.pusher = pusher
        self.keywords = keywords

        # 加载持久化状态
        self._load_state()

        logger.info(f"📧 邮件告警推送器启动")
        logger.info(f"🎯 关键字: {self.keywords}")
        logger.info(f"📬 邮件客户端: {len(self.email_clients)}个")
        logger.info(f"💾 已加载 {len(self.seen_uids)} 个已见邮件UID")
        logger.info(f"🧠 内存限制: 最大{self.max_seen_uids}个UID，清理阈值{self.cleanup_threshold}个")
        logger.info(f"⚡ 性能配置: 每次最多获取{self.max_emails_per_fetch}封邮件，并发超时{self.concurrent_timeout}秒")

    def _load_state(self):
        """加载已见邮件状态 - Linus式简洁实现"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # 加载UID列表
                    uid_data = data.get('seen_uids', [])
                    current_time = datetime.now(timezone.utc)

                    # 过滤过期的UID（防止无限增长）
                    valid_uids = []
                    for item in uid_data:
                        if isinstance(item, str):
                            # 兼容旧格式（只有UID）
                            valid_uids.append(item)
                        elif isinstance(item, dict):
                            # 新格式（包含时间戳）
                            uid = item.get('uid')
                            timestamp_str = item.get('timestamp')
                            if uid and timestamp_str:
                                try:
                                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                                    age_days = (current_time - timestamp).days
                                    if age_days <= self.max_uid_age_days:
                                        valid_uids.append(uid)
                                    else:
                                        logger.debug(f"清理过期UID: {uid} (已过期 {age_days} 天)")
                                except Exception:
                                    # 时间解析失败，保留UID（保守处理）
                                    valid_uids.append(uid)

                    self.seen_uids = set(valid_uids)
                    logger.info(f"💾 状态加载成功，有效UID: {len(self.seen_uids)}个")

            else:
                logger.info("💾 状态文件不存在，使用空状态")

        except Exception as e:
            logger.warning(f"💾 状态加载失败: {e}，使用空状态")
            self.seen_uids = set()

    def _save_state(self):
        """保存已见邮件状态 - 包含时间戳的新格式"""
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)

            # 构建包含时间戳的数据
            current_time = datetime.now(timezone.utc)
            uid_data = []

            for uid in self.seen_uids:
                uid_data.append({
                    'uid': uid,
                    'timestamp': current_time.isoformat()
                })

            data = {
                'seen_uids': uid_data,
                'last_save_time': current_time.isoformat(),
                'version': '2.0'  # 版本标识
            }

            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.debug(f"💾 状态保存成功，UID数量: {len(self.seen_uids)}")

        except Exception as e:
            logger.error(f"💾 状态保存失败: {e}")

    def _cleanup_old_uids(self):
        """智能清理UID - Linus式内存管理"""
        try:
            original_count = len(self.seen_uids)

            # 检查是否需要清理
            if original_count < self.cleanup_threshold:
                return

            logger.info(f"🧠 内存清理触发: 当前{original_count}个UID，阈值{self.cleanup_threshold}")

            # 如果有状态文件，基于时间清理
            if os.path.exists(self.state_file):
                self._cleanup_by_time()

            # 如果还是太多，基于数量强制清理（LRU策略）
            if len(self.seen_uids) > self.max_seen_uids:
                self._cleanup_by_count()

            cleaned_count = original_count - len(self.seen_uids)
            if cleaned_count > 0:
                logger.info(f"🧹 智能清理完成: 清理了{cleaned_count}个UID，剩余{len(self.seen_uids)}个")
                self._save_state()

        except Exception as e:
            logger.error(f"🧹 智能清理失败: {e}")

    def _cleanup_by_time(self):
        """基于时间的清理策略"""
        try:
            # 重新加载状态，自动过滤过期UID
            self._load_state()
        except Exception as e:
            logger.error(f"🧹 时间清理失败: {e}")

    def _cleanup_by_count(self):
        """基于数量的强制清理策略 - LRU"""
        try:
            current_count = len(self.seen_uids)
            if current_count <= self.max_seen_uids:
                return

            # 需要删除的数量
            to_remove = current_count - (self.max_seen_uids - self.cleanup_batch_size)

            # 简单策略：删除前N个UID（假设它们是最老的）
            # 在生产环境中，这里应该基于时间戳排序
            uids_list = list(self.seen_uids)
            uids_to_remove = uids_list[:to_remove]

            for uid in uids_to_remove:
                self.seen_uids.discard(uid)

            logger.warning(f"🧠 强制清理: 删除了{len(uids_to_remove)}个最老的UID")

        except Exception as e:
            logger.error(f"🧹 数量清理失败: {e}")

    def force_cleanup(self):
        """强制清理UID - 手动触发"""
        logger.info("🧹 开始强制清理...")
        original_count = len(self.seen_uids)
        self._cleanup_old_uids()
        cleaned_count = original_count - len(self.seen_uids)
        logger.info(f"🧹 强制清理完成，清理了 {cleaned_count} 个UID")
        return cleaned_count

    def add_seen_uid(self, uid: str):
        """添加已见UID - 带内存保护"""
        self.seen_uids.add(uid)

        # 检查是否需要立即清理
        if len(self.seen_uids) > self.max_seen_uids:
            logger.warning(f"🧠 内存超限: {len(self.seen_uids)} > {self.max_seen_uids}，立即清理")
            self._cleanup_by_count()

    def get_memory_stats(self) -> Dict:
        """获取内存使用统计"""
        return {
            'seen_uids_count': len(self.seen_uids),
            'max_seen_uids': self.max_seen_uids,
            'cleanup_threshold': self.cleanup_threshold,
            'memory_usage_percent': (len(self.seen_uids) / self.max_seen_uids) * 100
        }
    
    def initialize(self):
        """初始化：标记所有现有邮件为已见"""
        logger.info("🔧 初始化邮件推送器...")

        try:
            all_emails = self.fetch_all_emails_fast()
            logger.info(f"📧 发现邮箱中有 {len(all_emails)} 封邮件")

            # 标记所有现有邮件为已见（不推送历史邮件）
            for email in all_emails:
                self.seen_uids.add(email.uid)

            logger.info(f"✅ 初始化完成，已标记 {len(self.seen_uids)} 封邮件为已见")

        except Exception as e:
            logger.error(f"❌ 初始化失败: {e}")
            raise
    


    @monitor_performance("fetch_all_emails")
    def fetch_all_emails_fast(self) -> List:
        """并发获取所有邮件头信息 - Linus式性能优化 + 监控"""
        if not self.email_clients:
            return []

        # 单个邮箱直接处理，避免并发开销
        if len(self.email_clients) == 1:
            with PerformanceTimer(performance_monitor, "single_client_fetch"):
                return self._fetch_single_client_emails(self.email_clients[0])

        # 多邮箱并发处理 - 这是性能提升的关键
        all_emails = []
        failed_clients = []

        # 使用线程池并发获取邮件，最大5个线程
        max_workers = min(len(self.email_clients), 5)

        with PerformanceTimer(performance_monitor, "concurrent_fetch"):
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_client = {
                    executor.submit(self._fetch_single_client_emails, client): client
                    for client in self.email_clients
                }

                # 收集结果，使用配置的超时时间
                for future in concurrent.futures.as_completed(future_to_client, timeout=self.concurrent_timeout):
                    client = future_to_client[future]
                    email_addr = client.config.get('email', 'unknown')

                    try:
                        emails = future.result()
                        if emails:
                            all_emails.extend(emails)
                            logger.debug(f"📧 并发获取成功: {email_addr} - {len(emails)}封")
                        else:
                            logger.warning(f"📧 邮件客户端返回空: {email_addr}")
                            failed_clients.append(email_addr)

                    except Exception as e:
                        logger.error(f"📧 并发获取失败: {email_addr} - {e}")
                        failed_clients.append(email_addr)

        # 记录统计信息
        if failed_clients:
            logger.warning(f"⚠️ {len(failed_clients)} 个邮件客户端失败: {failed_clients}")

        logger.info(f"📧 并发获取完成，总计 {len(all_emails)} 封邮件")
        return all_emails

    def _fetch_single_client_emails(self, client) -> List:
        """获取单个客户端的邮件 - 线程安全版本"""
        try:
            # 检查连接状态
            if not hasattr(client, 'is_connected') or not client.is_connected:
                logger.debug(f"邮件客户端未连接，尝试重连: {client.config.get('email', 'unknown')}")
                if not client.connect():
                    return []

            # 获取邮件，使用配置的限制数量
            emails = client.fetch_emails(limit=self.max_emails_per_fetch)
            return emails if emails is not None else []

        except Exception as e:
            email_addr = client.config.get('email', 'unknown')
            logger.error(f"获取邮件失败 {email_addr}: {e}")

            # 尝试重置连接状态
            try:
                client.is_connected = False
                client.disconnect()
            except:
                pass

            return []

    def should_push_fast(self, email) -> bool:
        """快速判断是否应该推送邮件 - 只检查主题"""
        if not self.keywords:
            return True

        # 只检查主题，不检查内容（提升性能）
        subject_lower = email.subject.lower()

        for keyword in self.keywords:
            if keyword.lower() in subject_lower:
                logger.debug(f"邮件匹配关键字 '{keyword}': {email.subject}")
                return True

        return False

    def get_full_email_content(self, email):
        """按需获取完整邮件内容"""
        try:
            # 检查是否已经有真实的邮件内容（不是备用格式）
            if email.content and email.content.strip():
                # 如果内容不是备用格式，直接返回
                if not email.content.startswith("邮件主题:") and not email.content.startswith("(无法获取邮件正文内容)"):
                    logger.debug(f"邮件已有内容，直接返回: {email.subject} (长度: {len(email.content)})")
                    return email

            # 尝试从邮件客户端获取完整内容
            for client in self.email_clients:
                try:
                    # 使用新的方法根据UID获取完整内容
                    full_content = client.fetch_email_content_by_uid(email.uid)
                    if full_content and full_content.strip():
                        # 创建包含完整内容的邮件对象
                        email.content = full_content
                        logger.debug(f"成功获取邮件完整内容: {email.subject} (长度: {len(full_content)})")
                        return email
                except Exception as e:
                    logger.debug(f"从客户端获取邮件内容失败: {e}")
                    continue

            # 如果无法获取完整内容，使用基本信息
            if not email.content or not email.content.strip():
                email.content = f"邮件主题: {email.subject}\n发件人: {email.sender}\n\n(无法获取邮件正文内容)"
                logger.warning(f"无法获取邮件完整内容，使用基本信息: {email.subject}")

            return email

        except Exception as e:
            logger.error(f"获取完整邮件内容失败: {e}")
            # 确保有基本内容
            if not email.content:
                email.content = f"邮件主题: {email.subject}\n发件人: {email.sender}\n\n(获取内容时发生错误)"
            return email





    @monitor_performance("check_and_push_emails")
    def check_and_push_new_emails(self) -> bool:
        """核心逻辑：检查新邮件并推送 - 高性能版本 + 监控

        这是整个系统的核心 - Linus式的"好品味"体现：
        1. 基于UID的简单判断：if uid not in seen_uids -> 新邮件
        2. 没有复杂的时间比较和状态管理
        3. 消除了所有特殊情况
        4. 性能优化：只获取邮件头，按需获取内容
        5. 性能监控：记录关键指标
        """
        try:
            logger.info("🔍 开始检查新邮件...")
            start_time = time.time()

            # 快速获取邮件头信息（不获取内容，提升性能）
            all_emails = self.fetch_all_emails_fast()
            new_emails = []
            pushed_emails = []

            # 核心逻辑：基于UID检查新邮件 + 时间验证（Linus式可靠设计）
            current_time = datetime.now(timezone.utc)

            for email in all_emails:
                if email.uid not in self.seen_uids:
                    # 检查是否是真正的新邮件（添加时间验证）
                    if self._is_email_recent(email, current_time):
                        # 这是真正的新邮件！
                        self.add_seen_uid(email.uid)  # 使用内存保护的添加方法
                        new_emails.append(email)

                        logger.debug(f"🆕 发现新邮件: {email.subject}")

                        # 检查是否需要推送（只检查主题，提升性能）
                        if self.should_push_fast(email):
                            # 需要推送时才获取完整内容
                            full_email = self.get_full_email_content(email)
                            if full_email:
                                self.push_email(full_email)
                                pushed_emails.append(full_email)
                                logger.info(f"📤 推送: {email.subject}")
                        else:
                            logger.debug(f"⏭️ 跳过: {email.subject} (不匹配关键字)")
                    else:
                        # 历史邮件，只标记不推送
                        self.add_seen_uid(email.uid)  # 使用内存保护的添加方法
                        logger.debug(f"📜 跳过历史邮件: {email.subject} (时间过旧)")

            # 统计结果
            duration = time.time() - start_time
            memory_stats = self.get_memory_stats()

            logger.info(f"✅ 检查完成 (耗时: {duration:.1f}秒)")
            logger.info(f"📊 新邮件: {len(new_emails)}封, 推送: {len(pushed_emails)}封")
            logger.info(f"🧠 内存使用: {memory_stats['seen_uids_count']}/{memory_stats['max_seen_uids']} ({memory_stats['memory_usage_percent']:.1f}%)")

            # 保存状态（重要：确保状态持久化）
            self._save_state()

            # 智能清理：更频繁但更智能
            self.cleanup_counter += 1
            if self.cleanup_counter >= self.cleanup_interval:
                self.cleanup_counter = 0
                self._cleanup_old_uids()

                # 每次清理时输出性能摘要
                performance_monitor.log_performance_summary()

            return True

        except Exception as e:
            logger.error(f"❌ 检查邮件失败: {e}")
            return False

    def _is_email_recent(self, email, current_time: datetime) -> bool:
        """判断邮件是否是最近的 - Linus式时间验证

        核心原则：
        1. 只推送最近的邮件（默认1小时内）
        2. 容错处理：时间解析失败时保守处理
        3. 简单可靠：没有复杂的边界情况
        """
        try:
            email_time = email.date

            # 标准化时区
            if email_time.tzinfo is None:
                email_time = email_time.replace(tzinfo=timezone.utc)
            elif email_time.tzinfo != timezone.utc:
                email_time = email_time.astimezone(timezone.utc)

            # 计算时间差
            time_diff_seconds = (current_time - email_time).total_seconds()

            # 时间验证逻辑
            if time_diff_seconds < 0:
                # 邮件时间在未来（可能的时钟偏差）
                if abs(time_diff_seconds) <= 300:  # 5分钟容错
                    logger.debug(f"邮件时间在未来，但在容错范围内: {email.subject}")
                    return True
                else:
                    logger.debug(f"邮件时间过于未来，跳过: {email.subject}")
                    return False
            elif time_diff_seconds <= 3600:  # 1小时内
                # 最近的邮件，推送
                return True
            elif time_diff_seconds <= 7200:  # 2小时内，可能是延迟
                logger.debug(f"邮件稍旧但可能是延迟，推送: {email.subject}")
                return True
            else:
                # 历史邮件，不推送
                hours_old = time_diff_seconds / 3600
                logger.debug(f"邮件过旧 ({hours_old:.1f}小时)，不推送: {email.subject}")
                return False

        except Exception as e:
            # 时间解析失败，保守处理：不推送
            logger.warning(f"时间验证失败，跳过推送: {email.subject}, 错误: {e}")
            return False
    
    def should_push(self, email) -> bool:
        """判断是否应该推送邮件 - 只检查主题（Linus式简洁逻辑）"""
        if not self.keywords:
            # 没有关键字配置，推送所有邮件
            return True

        # 只检查主题，不检查内容（性能更好，逻辑更清晰）
        subject_lower = email.subject.lower()

        for keyword in self.keywords:
            if keyword.lower() in subject_lower:
                logger.debug(f"邮件匹配关键字 '{keyword}': {email.subject}")
                return True

        return False
    
    def push_email(self, email):
        """推送邮件 - 支持图片附件推送和仅图片模式"""
        if not self.pusher:
            logger.warning("未配置推送器，跳过推送")
            return False

        try:
            # 验证邮件数据完整性
            if not email or not email.subject:
                logger.warning("邮件数据不完整，跳过推送")
                return False

            # 获取图片附件配置
            image_config = config_manager.get_image_attachment_config()
            image_enabled = image_config.get("enabled", False)
            image_only = image_config.get("image_only", False)

            text_pushed = False
            image_pushed = False

            # 根据配置决定推送策略
            if image_enabled and image_only:
                # 仅推送图片模式
                logger.info(f"📷 仅图片模式推送: {email.subject}")
                image_pushed = self._push_image_attachments_if_enabled(email)

                if image_pushed:
                    logger.info(f"📤 仅图片推送成功: {email.subject}")
                    return True
                else:
                    logger.warning(f"📤 仅图片推送失败（无图片附件）: {email.subject}")
                    return False
            else:
                # 正常模式：推送正文（可能还有图片）
                success, message = self.pusher.push_message(
                    title=email.subject,
                    content=email.content or "",
                    sender=email.sender or "未知发件人",
                    date=email.date
                )

                if success:
                    text_pushed = True
                    logger.info(f"📤 邮件正文推送成功: {email.subject}")

                    # 如果启用了图片附件，也推送图片
                    if image_enabled:
                        image_pushed = self._push_image_attachments_if_enabled(email)

                    return True
                else:
                    logger.error(f"📤 邮件正文推送失败: {email.subject}, 错误: {message}")
                    return False

        except Exception as e:
            logger.error(f"📤 推送异常: {email.subject}, 错误: {e}")
            import traceback
            logger.debug(f"推送异常详情: {traceback.format_exc()}")
            return False

    def _push_image_attachments_if_enabled(self, email):
        """如果启用了图片附件推送，则推送图片附件

        Returns:
            bool: 是否成功推送了图片附件
        """
        try:
            # 获取图片附件配置
            image_config = config_manager.get_image_attachment_config()

            if not image_config.get("enabled", False):
                return False

            logger.info(f"📷 开始检查邮件图片附件: {email.subject}")

            # 查找对应的邮件客户端
            email_client = None
            for client in self.email_clients:
                if client.is_connected:
                    email_client = client
                    break

            if not email_client:
                logger.warning("📷 没有可用的邮件客户端，无法获取图片附件")
                return False

            # 提取图片附件
            attachments = attachment_handler.extract_from_email_uid(
                email_client, email.uid, image_config
            )

            if not attachments:
                logger.debug(f"📷 邮件无图片附件: {email.subject}")
                return False

            # 推送图片附件
            push_as_file = image_config.get("push_as_file", False)
            success, result_msg = self.pusher.push_image_attachments(attachments, push_as_file)

            if success:
                logger.info(f"📷 图片附件推送成功: {email.subject}, {result_msg}")
                return True
            else:
                logger.error(f"📷 图片附件推送失败: {email.subject}, {result_msg}")
                return False

        except Exception as e:
            logger.error(f"📷 图片附件推送异常: {email.subject}, 错误: {e}")
            import traceback
            logger.debug(f"图片附件推送异常详情: {traceback.format_exc()}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取推送器状态 - 增强版本"""
        return {
            "seen_emails": len(self.seen_uids),
            "keywords": self.keywords,
            "email_clients": len(self.email_clients),
            "state_file": self.state_file,
            "state_file_exists": os.path.exists(self.state_file),
            "max_uid_age_days": self.max_uid_age_days,
            "cleanup_counter": self.cleanup_counter,
            "cleanup_interval": self.cleanup_interval
        }

    def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细状态信息 - 用于调试"""
        status = self.get_status()

        # 添加状态文件信息
        if os.path.exists(self.state_file):
            try:
                stat = os.stat(self.state_file)
                status["state_file_size"] = stat.st_size
                status["state_file_modified"] = datetime.fromtimestamp(stat.st_mtime).isoformat()
            except Exception as e:
                status["state_file_error"] = str(e)

        # 添加邮件客户端状态
        client_status = []
        for i, client in enumerate(self.email_clients):
            client_info = {
                "index": i,
                "email": client.config.get('email', 'unknown'),
                "connected": getattr(client, 'is_connected', False)
            }
            client_status.append(client_info)
        status["client_details"] = client_status

        return status

