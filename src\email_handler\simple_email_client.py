#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的邮件客户端 - <PERSON><PERSON>式设计
只负责获取邮件，不判断新旧，不管理状态
"""

import imaplib
import email
import re
from datetime import datetime, timezone
from email.utils import parsedate_to_datetime
from typing import List, Dict, Any, Optional
from src.utils.logger import get_logger

logger = get_logger(__name__)


class EmailMessage:
    """邮件消息类"""

    def __init__(self, uid: str, subject: str, sender: str, date, content: str, is_new: bool = False):
        self.uid = uid
        self.subject = subject
        self.sender = sender
        self.date = date
        self.content = content
        self.is_new = is_new
        self.is_pushed = False


class SimpleEmailClient:
    """简化的邮件客户端 - 只负责获取邮件"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.imap = None
        self.is_connected = False

        # 增强的超时和重试配置 - <PERSON>us式精确控制
        self.connection_timeout = 15  # 连接超时：15秒（更快失败）
        self.read_timeout = 30       # 读取超时：30秒
        self.max_retries = 3         # 最大重试次数
        self.retry_delay_base = 1    # 重试延迟基数（秒）
        self.max_retry_delay = 8     # 最大重试延迟（秒）

        # 错误计数器
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
    
    def __del__(self):
        """析构函数，确保连接被正确关闭"""
        self.disconnect()
    
    def connect(self) -> bool:
        """连接到邮件服务器 - 简化版本，避免递归"""
        # 如果已连接且连接有效，直接返回
        if self.is_connected and self._test_connection():
            return True

        # 直接连接，暂时禁用连接池避免递归问题
        return self._direct_connect()

    def _direct_connect(self) -> bool:
        """直接连接到邮件服务器，带重试机制"""
        for attempt in range(self.max_retries):
            try:
                # 清理旧连接
                self.disconnect()

                server = self.config.get("server")
                port = self.config.get("port", 993)
                email_addr = self.config.get("email")
                password = self.config.get("password")

                if not all([server, email_addr, password]):
                    logger.error("邮箱配置不完整")
                    return False

                logger.debug(f"直连邮箱: {email_addr} (尝试 {attempt + 1}/{self.max_retries})")

                # 创建IMAP连接，设置超时
                if self.config.get("use_ssl", True):
                    self.imap = imaplib.IMAP4_SSL(server, port, timeout=self.connection_timeout)
                else:
                    self.imap = imaplib.IMAP4(server, port, timeout=self.connection_timeout)
                    if self.config.get("use_tls", True):
                        self.imap.starttls()

                # 登录
                from src.config.config_manager import config_manager
                decrypted_password = config_manager.decrypt_password(password)
                self.imap.login(email_addr, decrypted_password)

                # 选择收件箱
                self.imap.select("INBOX")

                self.is_connected = True
                logger.info(f"✅ 直连成功: {email_addr}")
                return True

            except Exception as e:
                self.consecutive_errors += 1
                error_type = self._classify_error(e)

                logger.warning(f"直连失败 (尝试 {attempt + 1}/{self.max_retries}): {error_type} - {e}")
                self.is_connected = False

                # 清理失败的连接
                if self.imap:
                    try:
                        self.imap.close()
                        self.imap.logout()
                    except:
                        pass
                    self.imap = None

                # 如果不是最后一次尝试，智能等待后重试
                if attempt < self.max_retries - 1:
                    wait_time = self._calculate_retry_delay(attempt, error_type)
                    logger.debug(f"等待 {wait_time} 秒后重试...")
                    import time
                    time.sleep(wait_time)

        logger.error(f"❌ 直连失败，已尝试 {self.max_retries} 次，连续错误: {self.consecutive_errors}")
        return False

    def _classify_error(self, error: Exception) -> str:
        """分类错误类型 - 用于智能重试"""
        error_str = str(error).lower()

        if 'timeout' in error_str or 'timed out' in error_str:
            return 'TIMEOUT'
        elif 'connection refused' in error_str or 'connection reset' in error_str:
            return 'CONNECTION_REFUSED'
        elif 'authentication failed' in error_str or 'login failed' in error_str:
            return 'AUTH_FAILED'
        elif 'ssl' in error_str or 'certificate' in error_str:
            return 'SSL_ERROR'
        elif 'network' in error_str or 'unreachable' in error_str:
            return 'NETWORK_ERROR'
        else:
            return 'UNKNOWN'

    def _calculate_retry_delay(self, attempt: int, error_type: str) -> float:
        """计算重试延迟 - Linus式智能退避"""
        base_delay = min(self.retry_delay_base * (2 ** attempt), self.max_retry_delay)

        # 根据错误类型调整延迟
        if error_type == 'AUTH_FAILED':
            return base_delay * 2  # 认证失败延迟更久
        elif error_type == 'TIMEOUT':
            return base_delay * 1.5  # 超时适当延迟
        elif error_type == 'CONNECTION_REFUSED':
            return base_delay * 2  # 连接拒绝延迟更久
        else:
            return base_delay
    
    def disconnect(self):
        """安全断开连接"""
        if self.imap:
            try:
                if self.is_connected:
                    try:
                        self.imap.close()
                    except Exception as e:
                        logger.debug(f"关闭IMAP连接时出错: {e}")
                    
                    try:
                        self.imap.logout()
                    except Exception as e:
                        logger.debug(f"登出IMAP时出错: {e}")
                
            except Exception as e:
                logger.debug(f"断开连接时出错: {e}")
            finally:
                self.imap = None
                self.is_connected = False
                logger.debug(f"已断开邮箱连接: {self.config.get('email', 'unknown')}")
    
    def _test_connection(self) -> bool:
        """测试IMAP连接是否有效"""
        try:
            if self.imap:
                self.imap.noop()
                return True
        except Exception as e:
            logger.debug(f"连接测试失败: {e}")
            return False
        return False
    
    def fetch_emails(self, limit: int = 200) -> List[EmailMessage]:
        """获取邮件列表 - 增强超时控制版本"""
        if not self.connect():
            return []

        try:
            import signal

            # 设置操作超时
            def timeout_handler(signum, frame):
                raise TimeoutError("邮件获取操作超时")

            # 在Windows上使用线程超时而不是信号
            import threading
            import time

            result = []
            exception_holder = [None]

            def fetch_worker():
                try:
                    # 搜索所有邮件
                    status, messages = self.imap.search(None, "ALL")
                    if status != "OK":
                        logger.error("搜索邮件失败")
                        return

                    message_ids = messages[0].split()
                    logger.debug(f"找到 {len(message_ids)} 封邮件")

                    # 智能限制邮件数量 - Linus式自适应策略
                    total_emails = len(message_ids)
                    max_emails = min(limit, total_emails)

                    if total_emails > max_emails:
                        logger.info(f"📧 邮件数量({total_emails})超过限制，获取最新的{max_emails}封")
                        message_ids = message_ids[-max_emails:]
                    else:
                        logger.debug(f"📧 获取全部{total_emails}封邮件")

                    # 批量获取邮件头信息（性能优化）
                    emails = self._fetch_emails_batch(message_ids)
                    result.extend(emails)

                except Exception as e:
                    exception_holder[0] = e

            # 启动工作线程
            worker_thread = threading.Thread(target=fetch_worker)
            worker_thread.daemon = True
            worker_thread.start()

            # 等待完成或超时
            worker_thread.join(timeout=self.read_timeout)

            if worker_thread.is_alive():
                logger.error(f"邮件获取超时 ({self.read_timeout}秒)")
                self.consecutive_errors += 1
                return []

            if exception_holder[0]:
                raise exception_holder[0]

            # 成功时重置错误计数
            self.consecutive_errors = 0
            logger.info(f"成功获取 {len(result)} 封邮件")
            return result

        except Exception as e:
            self.consecutive_errors += 1
            logger.error(f"获取邮件列表失败: {e}")

            # 如果连续错误过多，断开连接强制重连
            if self.consecutive_errors >= self.max_consecutive_errors:
                logger.warning(f"连续错误过多({self.consecutive_errors})，强制断开连接")
                self.disconnect()
                self.consecutive_errors = 0

            return []

    def _fetch_emails_batch(self, message_ids: List[bytes]) -> List[EmailMessage]:
        """真正的批量获取邮件 - Linus式性能优化"""
        emails = []

        if not message_ids:
            return emails

        try:
            # 构建批量获取的消息ID范围
            # IMAP支持范围查询，比如 "1:50" 或 "1,3,5:10"
            msg_ids_str = ','.join(msg_id.decode() for msg_id in reversed(message_ids))

            # 一次性批量获取所有邮件的头信息 - 这是关键优化！
            logger.debug(f"📦 批量获取 {len(message_ids)} 封邮件头信息...")
            status, data = self.imap.fetch(msg_ids_str, '(UID BODY[HEADER.FIELDS (SUBJECT FROM DATE)])')

            if status != "OK":
                logger.error("批量获取邮件头失败")
                return []

            # 解析批量返回的数据
            emails = self._parse_batch_email_data(data)
            logger.debug(f"📦 批量解析完成，成功解析 {len(emails)} 封邮件")

        except Exception as e:
            logger.error(f"批量获取邮件失败: {e}")
            # 降级到单个获取
            logger.info("🔄 降级到单个邮件获取模式...")
            emails = self._fetch_emails_one_by_one(message_ids)

        return emails

    def _parse_batch_email_data(self, data) -> List[EmailMessage]:
        """解析批量获取的邮件数据"""
        emails = []

        # IMAP批量返回的数据格式：[(b'1 (UID 1234 BODY[HEADER.FIELDS ...]', b'header_content'), b')', ...]
        i = 0
        while i < len(data):
            try:
                if isinstance(data[i], tuple) and len(data[i]) == 2:
                    header_line = data[i][0]
                    header_content = data[i][1]

                    # 解析UID
                    uid_match = re.search(r'UID (\d+)', header_line.decode())
                    uid = uid_match.group(1) if uid_match else f"unknown_{i}"

                    # 解析邮件头
                    email_msg = self._parse_email_header(uid, header_content)
                    if email_msg:
                        emails.append(email_msg)

                i += 1

            except Exception as e:
                logger.debug(f"解析单个邮件数据失败: {e}")
                i += 1
                continue

        return emails

    def _parse_email_header(self, uid: str, header_content) -> Optional[EmailMessage]:
        """解析邮件头信息"""
        try:
            if isinstance(header_content, bytes):
                header_text = header_content.decode('utf-8', errors='ignore')
            else:
                header_text = str(header_content)

            # 提取邮件头信息
            subject = ""
            sender = ""
            date_str = ""

            for line in header_text.split('\n'):
                line = line.strip()
                if line.lower().startswith('subject:'):
                    subject = self._decode_header(line[8:].strip())
                elif line.lower().startswith('from:'):
                    sender = self._decode_header(line[5:].strip())
                elif line.lower().startswith('date:'):
                    date_str = line[5:].strip()

            # 解析日期
            date = self._parse_email_date(date_str)

            return EmailMessage(
                uid=uid,
                subject=subject,
                sender=sender,
                date=date,
                content=""  # 头信息模式不获取内容
            )

        except Exception as e:
            logger.debug(f"解析邮件头失败: {e}")
            return None

    def _fetch_emails_one_by_one(self, message_ids: List[bytes]) -> List[EmailMessage]:
        """降级方案：逐个获取邮件"""
        emails = []

        for msg_id in reversed(message_ids):  # 最新的邮件在前
            try:
                email_msg = self._fetch_single_email_fast(msg_id)
                if email_msg:
                    emails.append(email_msg)
            except Exception as e:
                logger.error(f"获取邮件失败 {msg_id}: {e}")
                continue

        return emails

    def _parse_email_date(self, date_str: str) -> datetime:
        """解析邮件日期"""
        try:
            if date_str:
                date = parsedate_to_datetime(date_str)
                # 如果日期没有时区信息，添加UTC时区
                if date.tzinfo is None:
                    date = date.replace(tzinfo=timezone.utc)
                return date
            else:
                return datetime.now(timezone.utc)
        except Exception as e:
            logger.debug(f"解析邮件日期失败: {e}")
            return datetime.now(timezone.utc)
    
    def _fetch_single_email_fast(self, msg_id: bytes) -> Optional[EmailMessage]:
        """快速获取单个邮件 - 只获取头信息，不获取邮件体"""
        try:
            # 获取UID和邮件头信息（不获取邮件体，大幅提升性能）
            status, data = self.imap.fetch(msg_id, '(UID BODY[HEADER.FIELDS (SUBJECT FROM DATE)])')
            if status != "OK":
                return None

            # 解析UID
            uid_match = re.search(r'UID (\d+)', data[0][0].decode())
            uid = uid_match.group(1) if uid_match else msg_id.decode()

            # 解析邮件头
            header_data = data[0][1]
            if isinstance(header_data, bytes):
                header_text = header_data.decode('utf-8', errors='ignore')
            else:
                header_text = str(header_data)

            # 提取邮件头信息
            subject = ""
            sender = ""
            date_str = ""

            for line in header_text.split('\n'):
                line = line.strip()
                if line.lower().startswith('subject:'):
                    subject = self._decode_header(line[8:].strip())
                elif line.lower().startswith('from:'):
                    sender = self._decode_header(line[5:].strip())
                elif line.lower().startswith('date:'):
                    date_str = line[5:].strip()

            # 解析日期
            try:
                if date_str:
                    date = parsedate_to_datetime(date_str)
                    if date.tzinfo is None:
                        date = date.replace(tzinfo=timezone.utc)
                else:
                    date = datetime.now(timezone.utc)
            except Exception as e:
                logger.debug(f"解析邮件日期失败: {e}")
                date = datetime.now(timezone.utc)

            # 对于列表显示，不需要获取邮件内容（按需加载）
            return EmailMessage(
                uid=uid,
                subject=subject or "(无主题)",
                sender=sender or "(未知发件人)",
                date=date,
                content=""  # 列表显示时不需要内容，点击时再加载
            )

        except Exception as e:
            logger.error(f"快速获取邮件失败: {e}")
            return None

    def _fetch_single_email(self, msg_id: bytes) -> Optional[EmailMessage]:
        """获取单个邮件完整信息（包含内容）"""
        try:
            # 获取UID
            status, uid_data = self.imap.fetch(msg_id, '(UID)')
            if status == 'OK':
                uid_match = re.search(r'UID (\d+)', uid_data[0].decode())
                uid = uid_match.group(1) if uid_match else msg_id.decode()
            else:
                uid = msg_id.decode()

            # 获取邮件数据
            status, msg_data = self.imap.fetch(msg_id, "(RFC822)")
            if status != "OK":
                return None

            # 解析邮件
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)

            # 解析邮件头
            subject = self._decode_header(email_message.get("Subject", ""))
            sender = self._decode_header(email_message.get("From", ""))
            date_str = email_message.get("Date", "")

            # 解析日期
            try:
                if date_str:
                    date = parsedate_to_datetime(date_str)
                    # 如果日期没有时区信息，添加UTC时区
                    if date.tzinfo is None:
                        date = date.replace(tzinfo=timezone.utc)
                else:
                    date = datetime.now(timezone.utc)
            except Exception as e:
                logger.debug(f"解析邮件日期失败: {e}")
                date = datetime.now(timezone.utc)

            # 获取邮件内容
            content = self._get_email_content(email_message)

            return EmailMessage(
                uid=uid,
                subject=subject,
                sender=sender,
                date=date,
                content=content
            )

        except Exception as e:
            logger.error(f"解析邮件失败: {e}")
            return None

    def fetch_email_content_by_uid(self, uid: str) -> str:
        """根据UID获取邮件完整内容"""
        try:
            if not self.connect():
                return ""

            # 根据UID搜索邮件
            status, msg_ids = self.imap.uid('search', None, f'UID {uid}')
            if status != "OK" or not msg_ids[0]:
                logger.debug(f"未找到UID为{uid}的邮件")
                return ""

            # 获取完整邮件内容
            status, msg_data = self.imap.uid('fetch', uid, "(RFC822)")
            if status != "OK":
                logger.debug(f"获取UID为{uid}的邮件内容失败")
                return ""

            # 解析邮件
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)

            # 获取邮件内容
            content = self._get_email_content(email_message)
            return content

        except Exception as e:
            logger.error(f"根据UID获取邮件内容失败: {e}")
            return ""

    def _decode_header(self, header: str) -> str:
        """解码邮件头"""
        if not header:
            return ""
        
        try:
            from email.header import decode_header
            decoded_parts = decode_header(header)
            result = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        result += part.decode(encoding)
                    else:
                        result += part.decode('utf-8', errors='ignore')
                else:
                    result += part
            
            return result.strip()
            
        except Exception as e:
            logger.debug(f"解码邮件头失败: {e}")
            return str(header)
    
    def _get_email_content(self, email_message) -> str:
        """获取邮件内容"""
        try:
            content = ""

            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()

                    if content_type in ["text/plain", "text/html"]:
                        charset = part.get_content_charset() or 'utf-8'
                        payload = part.get_payload(decode=True)

                        if payload:
                            try:
                                text = payload.decode(charset, errors='ignore')
                                content += text + "\n"
                            except Exception as e:
                                logger.debug(f"解码邮件内容失败: {e}")
            else:
                charset = email_message.get_content_charset() or 'utf-8'
                payload = email_message.get_payload(decode=True)

                if payload:
                    try:
                        content = payload.decode(charset, errors='ignore')
                    except Exception as e:
                        logger.debug(f"解码邮件内容失败: {e}")

            # Linus式解密处理 - 简单直接
            content = self._decrypt_if_needed(content.strip())
            return content

        except Exception as e:
            logger.error(f"获取邮件内容失败: {e}")
            return ""

    def _decrypt_if_needed(self, content: str) -> str:
        """如果邮件内容被加密，则解密它 - 支持新的Unicode乱码格式"""
        try:
            # 检查是否是加密内容
            if "-----BEGIN ENCRYPTED CONTENT-----" not in content:
                return content  # 不是加密内容，直接返回

            # 提取加密数据
            start_marker = "-----BEGIN ENCRYPTED CONTENT-----"
            end_marker = "-----END ENCRYPTED CONTENT-----"

            start_idx = content.find(start_marker)
            end_idx = content.find(end_marker)

            if start_idx == -1 or end_idx == -1:
                logger.debug("加密标记不完整，返回原内容")
                return content

            encrypted_data = content[start_idx + len(start_marker):end_idx].strip()

            # 判断加密格式并解密
            if self._is_unicode_chaos_format(encrypted_data):
                # 新格式：Unicode乱码加密
                logger.debug("检测到Unicode乱码加密格式")
                decrypted_content = self._decrypt_unicode_chaos(encrypted_data)
            else:
                # 旧格式：Base64加密
                logger.debug("检测到Base64加密格式")
                decrypted_content = self._decrypt_base64_format(encrypted_data)

            logger.info("成功解密邮件内容")
            return decrypted_content

        except Exception as e:
            logger.warning(f"解密邮件失败: {e}")
            return content  # 解密失败返回原内容，不影响正常流程

    def _is_unicode_chaos_format(self, data: str) -> bool:
        """判断是否是Unicode乱码格式"""
        # 检查是否包含Unicode符号（而不是Base64字符）
        unicode_symbols = set('☀☁☂☃☄★☆☇☈☉☊☋☌☍☎☏☐☑☒☓☔☕☖☗☘☙☚☛☜☝☞☟☠☡☢☣☤☥☦☧☨☩☪☫☬☭☮☯☰☱☲☳☴☵☶☷☸☹☺☻☼☽☾☿♀')
        return any(char in unicode_symbols for char in data)

    def _decrypt_unicode_chaos(self, chaos_content: str) -> str:
        """解密Unicode乱码格式的内容"""
        # Unicode符号到Base64字符的映射表
        symbol_to_char = {
            '☀': 'A', '☁': 'B', '☂': 'C', '☃': 'D', '☄': 'E', '★': 'F', '☆': 'G', '☇': 'H',
            '☈': 'I', '☉': 'J', '☊': 'K', '☋': 'L', '☌': 'M', '☍': 'N', '☎': 'O', '☏': 'P',
            '☐': 'Q', '☑': 'R', '☒': 'S', '☓': 'T', '☔': 'U', '☕': 'V', '☖': 'W', '☗': 'X',
            '☘': 'Y', '☙': 'Z', '☚': 'a', '☛': 'b', '☜': 'c', '☝': 'd', '☞': 'e', '☟': 'f',
            '☠': 'g', '☡': 'h', '☢': 'i', '☣': 'j', '☤': 'k', '☥': 'l', '☦': 'm', '☧': 'n',
            '☨': 'o', '☩': 'p', '☪': 'q', '☫': 'r', '☬': 's', '☭': 't', '☮': 'u', '☯': 'v',
            '☰': 'w', '☱': 'x', '☲': 'y', '☳': 'z', '☴': '0', '☵': '1', '☶': '2', '☷': '3',
            '☸': '4', '☹': '5', '☺': '6', '☻': '7', '☼': '8', '☽': '9', '☾': '+', '☿': '/',
            '♀': '='
        }

        # Step 1: 将Unicode符号转换回Base64字符串
        b64_data = ""
        for symbol in chaos_content:
            b64_data += symbol_to_char.get(symbol, 'A')  # 未知符号默认为'A'

        # Step 2: 使用Base64解密流程
        return self._decrypt_base64_format(b64_data)

    def _decrypt_base64_format(self, encrypted_data: str) -> str:
        """解密Base64格式的内容（原有逻辑）"""
        from cryptography.fernet import Fernet
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
        import base64

        salt = b'alarm_monitor_salt_2024'
        password = "xjx001515"  # 企业固定密码

        kdf = PBKDF2HMAC(algorithm=hashes.SHA256(), length=32, salt=salt, iterations=100000)
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        f = Fernet(key)
        encrypted_bytes = base64.b64decode(encrypted_data)
        return f.decrypt(encrypted_bytes).decode('utf-8')
